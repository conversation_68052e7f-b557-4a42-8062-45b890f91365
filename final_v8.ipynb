

import os
import re
import json
from pathlib import Path
from tqdm import tqdm
import pandas as pd
from collections import defaultdict

from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph
from langchain_openai import AzureChatOpenAI

# Configuration
BASE_PATH = Path(r"C:/Shaik/sample/OneInsights")
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "final"


# Initialize components
graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)


 
llm = AzureChatOpenAI(
    api_key="********************************",
    azure_endpoint="https://azureopenaibrsc.openai.azure.com/",
    azure_deployment="gpt-4o",
    api_version="2024-12-01-preview"
)

# Global data structures
all_relationships = []
project_structure = {}
class_registry = {}

def clean_node_name(name):
    """Clean node names removing prefixes and extensions"""
    if not name:
        return name
    
    prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:', 'interface:']
    for prefix in prefixes_to_remove:
        if name.lower().startswith(prefix):
            name = name[len(prefix):]
    
    name = re.sub(r'\.(java|class)$', '', name, flags=re.IGNORECASE)
    return name.strip()

def is_random_variable(var_name):
    """Filter out random variables like loop counters"""
    if not var_name or len(var_name) <= 2:
        return True
    
    random_patterns = [
        r'^[ijklmn]$',  # Single letter loop counters
        r'^(temp|tmp)\d*$',  # Temporary variables
        r'^[a-z]\d+$',  # Single letter + numbers
        r'^(index|idx|cnt|count)\d*$',  # Common counter names
    ]
    
    for pattern in random_patterns:
        if re.match(pattern, var_name, re.IGNORECASE):
            return True
    
    return False

def extract_project_hierarchy():
    """Extract PROJECT -> APPLICATIONS -> FOLDERS -> FILES hierarchy"""
    project_name = BASE_PATH.name
    applications = set()
    
    # Add project node
    all_relationships.append({
        'source_node': 'OneInsight',
        'source_type': 'PROJECT',
        'destination_node': project_name,
        'destination_type': 'PROJECT',
        'relationship': 'CONTAINS',
        'file_path': None
    })
    
    for root, dirs, files in os.walk(BASE_PATH):
        rel_root = os.path.relpath(root, BASE_PATH)
        
        # Identify applications (top-level directories)
        if rel_root == '.':
            for d in dirs:
                applications.add(d)
                all_relationships.append({
                    'source_node': project_name,
                    'source_type': 'PROJECT',
                    'destination_node': d,
                    'destination_type': 'APPLICATION',
                    'relationship': 'CONTAINS',
                    'file_path': None
                })
        
        # Handle folder hierarchy
        path_parts = rel_root.split(os.sep) if rel_root != '.' else []
        if len(path_parts) > 0:
            parent = path_parts[0] if len(path_parts) == 1 else os.path.dirname(rel_root)
            current = os.path.basename(rel_root)
            
            parent_type = 'APPLICATION' if parent in applications else 'FOLDER'
            
            all_relationships.append({
                'source_node': parent,
                'source_type': parent_type,
                'destination_node': current,
                'destination_type': 'FOLDER',
                'relationship': 'CONTAINS',
                'file_path': None
            })
        
        # Handle files
        current_folder = os.path.basename(root) if rel_root != '.' else project_name
        for file in files:
            if file.endswith('.java'):
                all_relationships.append({
                    'source_node': current_folder,
                    'source_type': 'FOLDER' if rel_root != '.' else 'PROJECT',
                    'destination_node': file,
                    'destination_type': 'FILE',
                    'relationship': 'CONTAINS',
                    'file_path': os.path.relpath(os.path.join(root, file), BASE_PATH)
                })

def extract_package_and_imports(source_code_str):
    """Extract package declaration and imports"""
    package_pattern = r'package\s+([\w\.]+);'
    import_pattern = r'import\s+([\w\.]+);'
    package_match = re.search(package_pattern, source_code_str)
    package_name = package_match.group(1) if package_match else None
    import_matches = re.findall(import_pattern, source_code_str)
    return package_name, import_matches

def extract_api_endpoints(source_code_str):
    """Extract REST API endpoints"""
    endpoints = []
    
    mapping_patterns = {
        'RequestMapping': [
            r'@RequestMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']',
            r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@RequestMapping\s*\(\s*path\s*=\s*["\']([^"\']+)["\']'
        ],
        'GetMapping': [
            r'@GetMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@GetMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
        ],
        'PostMapping': [
            r'@PostMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@PostMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
        ],
        'PutMapping': [
            r'@PutMapping\s*\(\s*["\']([^"\']+)["\']'
        ],
        'DeleteMapping': [
            r'@DeleteMapping\s*\(\s*["\']([^"\']+)["\']'
        ]
    }
    
    for mapping_type, patterns in mapping_patterns.items():
        for pattern in patterns:
            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)
            for match in matches:
                if match.strip():
                    endpoints.append({
                        'type': mapping_type,
                        'path': match.strip(),
                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'
                    })
    
    return endpoints

def extract_database_entities(source_code_str):
    """Extract database tables, columns, datasets"""
    entities = []
    
    # Entity/Table detection
    entity_patterns = [
        r'@Entity\s*(?:\([^)]*\))?',
        r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']'
    ]
    
    for pattern in entity_patterns:
        if re.search(pattern, source_code_str, re.MULTILINE):
            table_matches = re.findall(r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']', source_code_str)
            for table_name in table_matches:
                if table_name.strip():
                    entities.append({
                        'type': 'Database/Table',
                        'name': table_name.strip()
                    })
            
            if not table_matches:
                class_match = re.search(r'public\s+class\s+(\w+)', source_code_str)
                if class_match:
                    class_name = class_match.group(1)
                    table_name = re.sub('([a-z0-9])([A-Z])', r'\1_\2', class_name).lower()
                    entities.append({
                        'type': 'Database/Table',
                        'name': table_name
                    })
    
    # Column detection from @Column annotations
    column_pattern = r'@Column\s*\(\s*name\s*=\s*["\']([^"\']+)["\']'
    column_matches = re.findall(column_pattern, source_code_str)
    for column_name in column_matches:
        if column_name.strip():
            entities.append({
                'type': 'Column',
                'name': column_name.strip()
            })
    
    # Query detection for datasets
    query_patterns = [
        r'@Query\s*\(\s*["\']([^"\']*(?:FROM|from)\s+([\w]+)[^"\']*)["\']',
        r'@Query\s*\(\s*value\s*=\s*["\']([^"\']*(?:FROM|from)\s+([\w]+)[^"\']*)["\']'
    ]
    
    for pattern in query_patterns:
        query_matches = re.findall(pattern, source_code_str, re.MULTILINE | re.IGNORECASE)
        for match in query_matches:
            if isinstance(match, tuple) and len(match) >= 2:
                table_name = match[1].strip()
                if table_name and table_name.lower() not in ['select', 'where', 'order', 'group']:
                    entities.append({
                        'type': 'Dataset',
                        'name': table_name
                    })
    
    return entities

def extract_class_relationships(source_code_str):
    """Extract extends and implements relationships"""
    relationships = []
    
    # Class extends
    class_extends_pattern = r'class\s+(\w+)\s+extends\s+([\w<>]+)'
    class_matches = re.findall(class_extends_pattern, source_code_str)
    for child_class, parent_class in class_matches:
        parent_class = re.sub(r'<.*?>', '', parent_class).strip()
        if parent_class:
            relationships.append({
                'child': child_class,
                'parent': parent_class,
                'type': 'EXTENDS'
            })
    
    # Class implements
    implements_pattern = r'class\s+(\w+)(?:\s+extends\s+\w+)?\s+implements\s+([\w<>,\s]+)'
    impl_matches = re.findall(implements_pattern, source_code_str)
    for class_name, implements_clause in impl_matches:
        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]
        for interface in interfaces:
            if interface:
                relationships.append({
                    'child': class_name,
                    'parent': interface,
                    'type': 'IMPLEMENTS'
                })
    
    # Interface extends
    interface_extends_pattern = r'interface\s+(\w+)\s+extends\s+([\w<>,\s]+)'
    matches = re.findall(interface_extends_pattern, source_code_str)
    for interface_name, extends_clause in matches:
        parent_interfaces = [part.strip().split('<')[0].strip() for part in extends_clause.split(',')]
        for parent in parent_interfaces:
            if parent:
                relationships.append({
                    'child': interface_name,
                    'parent': parent,
                    'type': 'EXTENDS'
                })
    
    return relationships

def build_class_registry():
    """Build comprehensive class registry with all metadata"""
    global class_registry
    
    for root, _, files in os.walk(BASE_PATH):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        source_code_str = f.read()
                    
                    package_name, imports = extract_package_and_imports(source_code_str)
                    endpoints = extract_api_endpoints(source_code_str)
                    db_entities = extract_database_entities(source_code_str)
                    class_relationships = extract_class_relationships(source_code_str)
                    
                    class_name = file.replace('.java', '')
                    fqcn = f'{package_name}.{class_name}' if package_name else class_name
                    
                    class_registry[class_name] = {
                        'fqcn': fqcn,
                        'package': package_name,
                        'file_path': file_path,
                        'imports': imports,
                        'endpoints': endpoints,
                        'db_entities': db_entities,
                        'class_relationships': class_relationships
                    }
                    
                except Exception as e:
                    print(f"Error processing {file}: {e}")
                    continue


def extract_ast_context(file_path):
    """Extract AST structure for LLM context only"""
    try:
        with open(file_path, 'rb') as f:
            source_code = f.read()

        tree = parser.parse(source_code)
        root_node = tree.root_node

        context_info = {
            'classes': [],
            'methods': [],
            'fields': [],
            'variables': []
        }

        def traverse_for_context(node, parent_type=None, parent_name=None):
            if node.type == 'class_declaration':
                for child in node.children:
                    if child.type == 'identifier':
                        class_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                        context_info['classes'].append(class_name)
                        for grandchild in node.children:
                            traverse_for_context(grandchild, 'class', class_name)
                        break

            elif node.type == 'method_declaration' and parent_type == 'class':
                for child in node.children:
                    if child.type == 'identifier':
                        method_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                        context_info['methods'].append(f"{parent_name}.{method_name}")
                        for grandchild in node.children:
                            traverse_for_context(grandchild, 'method', method_name)
                        break

            elif node.type == 'field_declaration' and parent_type == 'class':
                for child in node.children:
                    if child.type == 'variable_declarator':
                        for grandchild in child.children:
                            if grandchild.type == 'identifier':
                                field_name = source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8')
                                if not is_random_variable(field_name):
                                    context_info['fields'].append(f"{parent_name}.{field_name}")
                                break

            elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'method':
                for child in node.children:
                    if child.type == 'identifier':
                        var_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                        if not is_random_variable(var_name) and var_name != 'this':
                            context_info['variables'].append(f"{parent_name}.{var_name}")

            else:
                for child in node.children:
                    traverse_for_context(child, parent_type, parent_name)

        traverse_for_context(root_node)
        return context_info

    except Exception as e:
        print(f"Error extracting AST context from {file_path}: {e}")
        return {'classes': [], 'methods': [], 'fields': [], 'variables': []}

def build_enhanced_system_prompt(file_path, ast_context, class_registry):
    """Build system prompt with AST context for LLM"""
    registry_context = 'Known Classes:\n'
    for class_name, info in class_registry.items():
        registry_context += f'- {class_name} (FQCN: {info["fqcn"]})\n'
        if len(info.get('endpoints', [])) > 0:
            registry_context += f'  * {len(info["endpoints"])} API endpoint(s)\n'
        if len(info.get('db_entities', [])) > 0:
            registry_context += f'  * {len(info["db_entities"])} DB entity/entities\n'

    ast_context_str = f"""
AST CONTEXT (for reference only):
Classes: {', '.join(ast_context.get('classes', []))}
Methods: {', '.join(ast_context.get('methods', [])[:10])}  # Limit for brevity
Fields: {', '.join(ast_context.get('fields', [])[:10])}
Variables: {', '.join(ast_context.get('variables', [])[:10])}
"""

    prompt = f"""
You are a Java code lineage extraction engine. Extract relationships between code entities with STRICT focus on:

CONTEXT:
{registry_context}

{ast_context_str}

CRITICAL RULES - FOLLOW EXACTLY:
1. Use SIMPLE names only (remove prefixes like "method:", "class:", etc.)
2. MANDATORY NODE TYPES: PROJECT, APPLICATION, FOLDER, FILE, CLASS, METHOD, VARIABLE, ENDPOINT, Database/Table, Column, Dataset, API Endpoint, Collection
3. MANDATORY RELATIONSHIPS: CONTAINS, DATA_FIND, DECLARES, DECLARES_VARIABLE, EXPOSES, EXTENDS, HAS_FIELD, IMPLEMENTS, TRANSFORMS_TO, USES, READS_FROM, WRITES_TO, PRODUCES, CONSUMES, HAS_ELEMENT, ADDED_TO_COLLECTION
4. RELATIONSHIP DIRECTIONS (DO NOT REVERSE):
   - FILE -[DECLARES]-> CLASS
   - CLASS -[DECLARES]-> METHOD
   - CLASS -[HAS_FIELD]-> VARIABLE
   - METHOD -[USES]-> VARIABLE
   - CLASS -[DECLARES]-> ENDPOINT
   - CLASS -[TRANSFORMS_TO]-> Database/Table
   - METHOD -[READS_FROM]-> Database/Table
   - METHOD -[WRITES_TO]-> Database/Table
5. Extract REST API endpoints as 'ENDPOINT' nodes (GET /api/users, POST /api/data)
6. Extract database tables from @Entity, @Table, @Query annotations as 'Database/Table' nodes
7. Extract @Column annotations as 'Column' nodes
8. Extract interface extends and class implements relationships
9. NEVER create reverse relationships (method->class, variable->method, etc.)
10. Avoid random variables like i, j, temp, tmp, index, idx
11. Focus on meaningful business logic variables and transformations

Extract triples in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the triples, no explanations.
"""
    return prompt


def process_with_llm():
    """Process files with LLM using AST context"""
    splitter = RecursiveCharacterTextSplitter.from_language(
        language=LC_Language.JAVA,
        chunk_size=4000,
        chunk_overlap=200
    )

    java_docs, split_docs = [], []
    for root, _, files in os.walk(BASE_PATH):
        for file in files:
            if file.endswith('.java'):
                try:
                    loader = TextLoader(os.path.join(root, file))
                    java_docs.extend(loader.load())
                except Exception as e:
                    print(f"Error loading {file}: {e}")
                    continue

    for doc in java_docs:
        split_docs.extend(splitter.split_documents([doc]))

    for chunk in tqdm(split_docs, desc='Processing with LLM'):
        file_path = chunk.metadata.get('source')
        ast_context = extract_ast_context(file_path)
        system_prompt = build_enhanced_system_prompt(file_path, ast_context, class_registry)

        transformer = LLMGraphTransformer(
            llm=llm,
            additional_instructions=system_prompt,
            allowed_nodes=['PROJECT', 'APPLICATION', 'FOLDER', 'FILE', 'CLASS', 'METHOD', 'VARIABLE', 'ENDPOINT', 'Database/Table', 'Column', 'Dataset', 'API Endpoint', 'Collection'],
            allowed_relationships=[
                ('PROJECT', 'CONTAINS', 'APPLICATION'),
                ('APPLICATION', 'CONTAINS', 'FOLDER'),
                ('FOLDER', 'CONTAINS', 'FILE'),
                ('FILE', 'DECLARES', 'CLASS'),
                ('CLASS', 'DECLARES', 'METHOD'),
                ('CLASS', 'HAS_FIELD', 'VARIABLE'),
                ('METHOD', 'USES', 'VARIABLE'),
                ('CLASS', 'DECLARES', 'ENDPOINT'),
                ('CLASS', 'TRANSFORMS_TO', 'Database/Table'),
                ('METHOD', 'READS_FROM', 'Database/Table'),
                ('METHOD', 'WRITES_TO', 'Database/Table'),
                ('CLASS', 'EXTENDS', 'CLASS'),
                ('CLASS', 'IMPLEMENTS', 'CLASS'),
                ('METHOD', 'CALLS', 'METHOD'),
                ('VARIABLE', 'TRANSFORMS_TO', 'VARIABLE'),
                ('Database/Table', 'HAS_ELEMENT', 'Column'),
                ('Collection', 'ADDED_TO_COLLECTION', 'Dataset'),
            ],
            strict_mode=True,
            node_properties=False,
            relationship_properties=False,
        )

        try:
            graph_docs = transformer.convert_to_graph_documents([chunk])
            for graph_doc in graph_docs:
                for node in graph_doc.nodes:
                    node.id = clean_node_name(node.id)

                for rel in graph_doc.relationships:
                    rel.source.id = clean_node_name(rel.source.id)
                    rel.target.id = clean_node_name(rel.target.id)

                    all_relationships.append({
                        'source_node': rel.source.id,
                        'source_type': rel.source.type,
                        'destination_node': rel.target.id,
                        'destination_type': rel.target.type,
                        'relationship': rel.type,
                        'file_path': file_path
                    })
        except Exception as e:
            print(f"Error processing chunk from {file_path}: {e}")
            continue



def extract_code_structure_relationships():
    """Extract detailed code structure relationships"""
    for class_name, info in class_registry.items():
        file_name = os.path.basename(info['file_path'])

        # FILE -> CLASS
        all_relationships.append({
            'source_node': file_name,
            'source_type': 'FILE',
            'destination_node': class_name,
            'destination_type': 'CLASS',
            'relationship': 'DECLARES',
            'file_path': info['file_path']
        })

        # CLASS -> ENDPOINT relationships
        for endpoint in info.get('endpoints', []):
            endpoint_name = f"{endpoint['method']} {endpoint['path']}"
            all_relationships.append({
                'source_node': class_name,
                'source_type': 'CLASS',
                'destination_node': endpoint_name,
                'destination_type': 'ENDPOINT',
                'relationship': 'EXPOSES',
                'file_path': info['file_path']
            })

        # CLASS -> Database/Table relationships
        for entity in info.get('db_entities', []):
            all_relationships.append({
                'source_node': class_name,
                'source_type': 'CLASS',
                'destination_node': entity['name'],
                'destination_type': entity['type'],
                'relationship': 'TRANSFORMS_TO' if entity['type'] == 'Database/Table' else 'HAS_ELEMENT',
                'file_path': info['file_path']
            })

        # CLASS inheritance relationships
        for rel in info.get('class_relationships', []):
            all_relationships.append({
                'source_node': rel['child'],
                'source_type': 'CLASS',
                'destination_node': rel['parent'],
                'destination_type': 'CLASS',
                'relationship': rel['type'],
                'file_path': info['file_path']
            })

def push_to_neo4j():
    """Push all relationships to Neo4j"""
    print("Clearing existing data...")
    graph.query("MATCH (n) DETACH DELETE n")

    df_all = pd.DataFrame(all_relationships)

    print(f"Pushing {len(df_all)} relationships to Neo4j...")

    # Create nodes
    for node_type in df_all['source_type'].unique():
        nodes = df_all[df_all['source_type'] == node_type]['source_node'].unique()
        for node in nodes:
            graph.query(f"MERGE (n:{node_type} {{name: $name}})", {'name': node})

    for node_type in df_all['destination_type'].unique():
        nodes = df_all[df_all['destination_type'] == node_type]['destination_node'].unique()
        for node in nodes:
            graph.query(f"MERGE (n:{node_type} {{name: $name}})", {'name': node})

    # Create relationships
    for _, row in tqdm(df_all.iterrows(), total=len(df_all), desc="Creating relationships"):
        query = f"""
        MATCH (source:{row['source_type']} {{name: $source_name}})
        MATCH (target:{row['destination_type']} {{name: $target_name}})
        MERGE (source)-[:{row['relationship']}]->(target)
        """
        graph.query(query, {
            'source_name': row['source_node'],
            'target_name': row['destination_node']
        })

    print("✅ Data successfully pushed to Neo4j!")