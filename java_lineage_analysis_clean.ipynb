# ========== CONFIGURATION AND IMPORTS ==========
import os
from pathlib import Path
from tqdm import tqdm
import pandas as pd
import re
from collections import defaultdict

from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph

# Configuration
BASE_PATH = Path(r"C:/Shaik/sample/OneInsights")
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "variable"
GOOGLE_API_KEY = "AIzaSyBo3Wp2wFlgKacfGOXTFUXBqU6p1F6ATAY"

# Initialize components
graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

llm = ChatGoogleGenerativeAI(
    model="gemini-2.5-pro",
    temperature=0,
    google_api_key=GOOGLE_API_KEY
)

print("✅ Configuration and imports completed successfully")

# ========== UTILITY FUNCTIONS ==========

def read_source_code(file_path):
    """Read Java source code file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read().encode('utf-8')

def extract_package_and_imports(source_code_str):
    """Extract package name and imports from Java source"""
    package_pattern = r'package\s+([\w\.]+);'
    import_pattern = r'import\s+([\w\.]+);'
    package_match = re.search(package_pattern, source_code_str)
    package_name = package_match.group(1) if package_match else None
    import_matches = re.findall(import_pattern, source_code_str)
    return package_name, import_matches

def standardize_variable_name(class_name, method_name, variable_name):
    """Standardize variable names as class.method.variable format"""
    if not all([class_name, method_name, variable_name]):
        return variable_name
    return f"{class_name}.{method_name}.{variable_name}"

def clean_node_name(name):
    """Clean node names to remove prefixes and suffixes"""
    if not name:
        return name
    
    # Remove common prefixes
    prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']
    for prefix in prefixes_to_remove:
        if name.lower().startswith(prefix):
            name = name[len(prefix):]
    
    # Remove file extensions
    name = re.sub(r'\.(java|class)$', '', name, flags=re.IGNORECASE)
    
    return name.strip()

def normalize_nodes(df):
    """Normalize node names in DataFrame"""
    df = df.copy()
    for col in ["source_node", "destination_node"]:
        df[col] = df[col].astype(str).apply(lambda x: x.replace("\\", "/").split("/")[-1])
    return df

print("✅ Utility functions defined")

# ========== STAGE 1: PROJECT AND FOLDER HIERARCHY ==========

def extract_project_structure(base_path):
    """Extract project, application, and folder hierarchy"""
    project_records = []
    folder_records = []
    file_records = []
    
    base_path = os.path.abspath(base_path)
    project_name = os.path.basename(base_path)
    processed_folders = set()
    
    # Create PROJECT node
    project_records.append({
        'source_node': 'OneInsight',
        'source_type': 'PROJECT',
        'destination_node': project_name,
        'destination_type': 'APPLICATION',
        'relationship': 'CONTAINS',
        'file_path': None
    })
    
    # Identify main applications (ServiceBolt, UnifiedBolt)
    applications = ['ServiceBolt', 'UnifiedBolt']
    for app in applications:
        app_path = os.path.join(base_path, app)
        if os.path.exists(app_path):
            project_records.append({
                'source_node': project_name,
                'source_type': 'APPLICATION',
                'destination_node': app,
                'destination_type': 'APPLICATION',
                'relationship': 'CONTAINS',
                'file_path': None
            })
    
    # Extract folder and file hierarchy
    for root, dirs, files in os.walk(base_path):
        rel_root = os.path.relpath(root, base_path)
        parent_folder = project_name if rel_root == '.' else os.path.dirname(rel_root) or project_name
        current_folder = project_name if rel_root == '.' else os.path.basename(rel_root)

        folder_key = f'{parent_folder}->{current_folder}'
        if folder_key not in processed_folders and parent_folder != current_folder:
            folder_records.append({
                'source_node': parent_folder,
                'source_type': 'FOLDER',
                'destination_node': current_folder,
                'destination_type': 'FOLDER',
                'relationship': 'CONTAINS',
                'file_path': None
            })
            processed_folders.add(folder_key)

        # Process Java files
        for f in files:
            if f.endswith('.java'):
                file_rel_path = os.path.relpath(os.path.join(root, f), base_path)
                file_records.append({
                    'source_node': current_folder,
                    'source_type': 'FOLDER',
                    'destination_node': f,
                    'destination_type': 'FILE',
                    'relationship': 'CONTAINS',
                    'file_path': file_rel_path
                })
    
    return project_records, folder_records, file_records

# Execute Stage 1
project_records, folder_records, file_records = extract_project_structure(BASE_PATH)
df_project = pd.DataFrame(project_records)
df_folders = pd.DataFrame(folder_records)
df_files = pd.DataFrame(file_records)

print(f'✅ Stage 1 Complete: {len(df_project)} project relationships, {len(df_folders)} folder relationships, {len(df_files)} file relationships')
print(f'📊 Project Structure: {df_project.shape[0]} project nodes, {df_folders.shape[0]} folder nodes, {df_files.shape[0]} file nodes')

# ========== STAGE 2: ENHANCED AST EXTRACTION WITH VARIABLE TRANSFORMATION ==========

def extract_enhanced_ast_structure(file_path):
    """Extract AST structure with enhanced variable transformations"""
    records = []
    source_code = read_source_code(file_path)
    tree = parser.parse(source_code)
    root_node = tree.root_node
    file_name = os.path.basename(file_path)
    
    current_class = None
    current_method = None
    
    def traverse_enhanced(node, parent_type=None, parent_name=None):
        nonlocal current_class, current_method
        
        # Handle class declarations
        if node.type == 'class_declaration':
            for child in node.children:
                if child.type == 'identifier':
                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    current_class = class_name
                    
                    # FILE -> CLASS relationship
                    records.append({
                        'source_node': file_name,
                        'source_type': 'FILE',
                        'destination_node': class_name,
                        'destination_type': 'CLASS',
                        'relationship': 'DECLARES',
                        'file_path': file_path
                    })
                    break
            
            # Continue traversing with class context
            for child in node.children:
                traverse_enhanced(child, 'CLASS', current_class)
                
        # Handle method declarations with enhanced variable tracking
        elif node.type == 'method_declaration':
            for child in node.children:
                if child.type == 'identifier':
                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    current_method = method_name
                    
                    # CLASS -> METHOD relationship
                    if current_class:
                        records.append({
                            'source_node': current_class,
                            'source_type': 'CLASS',
                            'destination_node': method_name,
                            'destination_type': 'METHOD',
                            'relationship': 'DECLARES',
                            'file_path': file_path
                        })
                    break
            
            # Continue traversing with method context
            for child in node.children:
                traverse_enhanced(child, 'METHOD', current_method)
                
        # Handle field declarations (class-level variables)
        elif node.type == 'field_declaration':
            for child in node.children:
                if child.type == 'variable_declarator':
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))
                            
                            # Enhanced variable name with class context
                            enhanced_field_name = f"{current_class}.{field_name}" if current_class else field_name
                            
                            # CLASS -> VARIABLE relationship
                            if current_class:
                                records.append({
                                    'source_node': current_class,
                                    'source_type': 'CLASS',
                                    'destination_node': enhanced_field_name,
                                    'destination_type': 'VARIABLE',
                                    'relationship': 'HAS_FIELD',
                                    'file_path': file_path
                                })
                                
        # Handle variable usage in methods with enhanced transformation
        elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'METHOD':
            for child in node.children:
                if child.type == 'identifier':
                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if var_name and var_name != 'this' and current_method and current_class:
                        # Enhanced variable transformation: class.method.variable
                        enhanced_var_name = standardize_variable_name(current_class, current_method, var_name)
                        
                        # METHOD -> VARIABLE relationship
                        records.append({
                            'source_node': current_method,
                            'source_type': 'METHOD',
                            'destination_node': enhanced_var_name,
                            'destination_type': 'VARIABLE',
                            'relationship': 'DECLARES_VARIABLE',
                            'file_path': file_path
                        })
                        
                        # Enhanced transformation tracking
                        assignment_text = source_code[node.start_byte:node.end_byte].decode('utf-8')
                        
                        # Check for method calls (transformations like setPatternCount)
                        if 'set' in assignment_text.lower() or '=' in assignment_text:
                            # Extract operation details
                            operation_match = re.search(r'\.(\w+)\s*\(', assignment_text)
                            if operation_match:
                                operation_name = operation_match.group(1)
                                
                                # Create OPERATION node and relationships
                                records.append({
                                    'source_node': enhanced_var_name,
                                    'source_type': 'VARIABLE',
                                    'destination_node': operation_name,
                                    'destination_type': 'OPERATION',
                                    'relationship': 'UPDATED_BY',
                                    'file_path': file_path
                                })
                                
                                # Extract value assignment
                                value_match = re.search(r'\(([^)]+)\)', assignment_text)
                                if value_match:
                                    value_expr = value_match.group(1)
                                    records.append({
                                        'source_node': operation_name,
                                        'source_type': 'OPERATION',
                                        'destination_node': value_expr,
                                        'destination_type': 'VALUE',
                                        'relationship': 'ASSIGNS_VALUE',
                                        'file_path': file_path
                                    })
                        
                        # Track variable transformations (like temp -> b)
                        if '=' in assignment_text:
                            # Look for variable-to-variable assignments
                            transform_match = re.search(r'(\w+)\s*=\s*(\w+)', assignment_text)
                            if transform_match:
                                target_var = transform_match.group(1)
                                source_var = transform_match.group(2)
                                
                                if target_var != source_var and target_var != var_name:
                                    source_enhanced = standardize_variable_name(current_class, current_method, source_var)
                                    target_enhanced = standardize_variable_name(current_class, current_method, target_var)
                                    
                                    records.append({
                                        'source_node': source_enhanced,
                                        'source_type': 'VARIABLE',
                                        'destination_node': target_enhanced,
                                        'destination_type': 'VARIABLE',
                                        'relationship': 'TRANSFORMS',
                                        'file_path': file_path
                                    })
                        
        # Handle field access and method calls
        elif node.type == 'field_access' and parent_type == 'METHOD':
            for child in node.children:
                if child.type == 'identifier':
                    field_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if field_name and field_name != 'this' and current_method and current_class:
                        enhanced_field_name = standardize_variable_name(current_class, current_method, field_name)
                        
                        # METHOD -> VARIABLE relationship (usage)
                        records.append({
                            'source_node': current_method,
                            'source_type': 'METHOD',
                            'destination_node': enhanced_field_name,
                            'destination_type': 'VARIABLE',
                            'relationship': 'USES',
                            'file_path': file_path
                        })
        else:
            # Continue traversing for other node types
            for child in node.children:
                traverse_enhanced(child, parent_type, parent_name)

    traverse_enhanced(root_node)
    return records

# Execute Stage 2
ast_records = []
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            file_path = os.path.join(root, file)
            try:
                ast_records.extend(extract_enhanced_ast_structure(file_path))
            except Exception as e:
                print(f'⚠️ Error processing {file}: {e}')
                continue

df_ast = pd.DataFrame(ast_records)
print(f'✅ Stage 2 Complete: {len(df_ast)} enhanced AST relationships extracted')
print(f'📊 Variable Transformations: {len(df_ast[df_ast["relationship"] == "DECLARES_VARIABLE"])} standardized variables')

# ========== STAGE 3: API ENDPOINT AND DATABASE ENTITY EXTRACTION ==========

def extract_api_endpoints(source_code_str):
    """Extract REST API endpoints from Spring annotations"""
    endpoints = []
    
    # Enhanced patterns for API endpoint detection
    mapping_patterns = {
        'RequestMapping': [
            r'@RequestMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']',
            r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@RequestMapping\s*\(\s*path\s*=\s*["\']([^"\']+)["\']'
        ],
        'GetMapping': [
            r'@GetMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@GetMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
        ],
        'PostMapping': [
            r'@PostMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@PostMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
        ],
        'PutMapping': [
            r'@PutMapping\s*\(\s*["\']([^"\']+)["\']'
        ],
        'DeleteMapping': [
            r'@DeleteMapping\s*\(\s*["\']([^"\']+)["\']'
        ]
    }
    
    for mapping_type, patterns in mapping_patterns.items():
        for pattern in patterns:
            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)
            for match in matches:
                if match.strip():
                    endpoints.append({
                        'type': mapping_type,
                        'path': match.strip(),
                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'
                    })
    
    return endpoints

def extract_database_entities(source_code_str):
    """Extract database entities from JPA annotations"""
    entities = []
    
    # Entity detection patterns
    entity_patterns = [
        r'@Entity\s*(?:\([^)]*\))?',
        r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']'
    ]
    
    for pattern in entity_patterns:
        if re.search(pattern, source_code_str, re.MULTILINE):
            # Extract table name from @Table annotation
            table_matches = re.findall(r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']', source_code_str)
            for table_name in table_matches:
                if table_name.strip():
                    entities.append({
                        'type': 'table',
                        'name': table_name.strip()
                    })
    
    return entities

def build_enhanced_class_registry():
    """Build comprehensive class registry with endpoints and entities"""
    class_registry = {}
    endpoint_records = []
    data_records = []
    
    for root, _, files in os.walk(BASE_PATH):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        source_code_str = f.read()
                    
                    package_name, imports = extract_package_and_imports(source_code_str)
                    endpoints = extract_api_endpoints(source_code_str)
                    db_entities = extract_database_entities(source_code_str)
                    
                    class_name = file.replace('.java', '')
                    fqcn = f'{package_name}.{class_name}' if package_name else class_name
                    
                    class_registry[class_name] = {
                        'fqcn': fqcn,
                        'package': package_name,
                        'file_path': file_path,
                        'imports': imports,
                        'endpoints': endpoints,
                        'db_entities': db_entities
                    }
                    
                    # Create CLASS -> ENDPOINT relationships
                    for ep in endpoints:
                        endpoint_name = f"{ep['method']} {ep['path']}"
                        endpoint_records.append({
                            'source_node': class_name,
                            'source_type': 'CLASS',
                            'destination_node': endpoint_name,
                            'destination_type': 'ENDPOINT',
                            'relationship': 'EXPOSES',
                            'file_path': file_path
                        })
                    
                    # Create CLASS -> DATA relationships
                    for entity in db_entities:
                        data_records.append({
                            'source_node': class_name,
                            'source_type': 'CLASS',
                            'destination_node': entity['name'],
                            'destination_type': 'DATA',
                            'relationship': 'DATA_FIND',
                            'file_path': file_path
                        })
                        
                except Exception as e:
                    print(f'⚠️ Error processing {file}: {e}')
                    continue
    
    return class_registry, endpoint_records, data_records

# Execute Stage 3
class_registry, endpoint_records, data_records = build_enhanced_class_registry()
df_endpoints = pd.DataFrame(endpoint_records)
df_data = pd.DataFrame(data_records)

print(f'✅ Stage 3 Complete: {len(class_registry)} classes analyzed')
print(f'📊 API Endpoints: {len(df_endpoints)} endpoint relationships')
print(f'📊 Data Entities: {len(df_data)} data relationships')

from langchain_openai import AzureChatOpenAI

llm = AzureChatOpenAI(
    api_key="********************************",
    azure_endpoint="https://azureopenaibrsc.openai.azure.com/",
    azure_deployment="gpt-4o",
    api_version="2024-12-01-preview"
)

# ========== STAGE 4: LLM-ENHANCED PROCESSING ==========

def build_enhanced_system_prompt():
    """Build system prompt for LLM processing with focus on variable transformations"""
    prompt = f"""
You are a Java code lineage extraction engine with focus on enhanced variable transformations.

CRITICAL TRANSFORMATION RULES:
1. **Variable Standardization**: Transform all variables to class.method.variable format
   Example: For processFailure method in ProcessFailure class:
   - ctx → ProcessFailure.processFailure.ctx
   - failurePatternRepo → ProcessFailure.processFailure.failurePatternRepo
   - tempBuildFailure → ProcessFailure.processFailure.tempBuildFailure

2. **Relationship Focus**: Extract these specific relationships:
   - PROJECT -[CONTAINS]-> APPLICATION
   - APPLICATION -[CONTAINS]-> FOLDER
   - FOLDER -[CONTAINS]-> FILE
   - FILE -[DECLARES]-> CLASS
   - CLASS -[DECLARES]-> METHOD
   - CLASS -[HAS_FIELD]-> VARIABLE
   - METHOD -[DECLARES_VARIABLE]-> VARIABLE
   - METHOD -[USES]-> VARIABLE
   - CLASS -[EXPOSES]-> ENDPOINT
   - CLASS -[DATA_FIND]-> DATA
   - VARIABLE -[TRANSFORMS_TO]-> DATA
   - CLASS -[EXTENDS]-> CLASS
   - CLASS -[IMPLEMENTS]-> INTERFACE

3. **Node Types**: Focus on PROJECT, APPLICATION, FOLDER, FILE, CLASS, METHOD, VARIABLE, ENDPOINT, DATA

4. **Variable Tracking**: Track variable state changes and transformations throughout the pipeline

Extract relationships in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the relationships, no explanations.
"""
    return prompt

def process_with_llm():
    """Process code chunks with LLM for enhanced extraction"""
    # Prepare text splitter
    splitter = RecursiveCharacterTextSplitter.from_language(
        language=LC_Language.JAVA,
        chunk_size=4000,
        chunk_overlap=200
    )
    
    # Load and split documents
    java_docs = []
    for root, _, files in os.walk(BASE_PATH):
        for file in files:
            if file.endswith('.java'):
                try:
                    loader = TextLoader(os.path.join(root, file))
                    java_docs.extend(loader.load())
                except Exception as e:
                    print(f"⚠️ Error loading {file}: {e}")
                    continue
    
    split_docs = []
    for doc in java_docs:
        split_docs.extend(splitter.split_documents([doc]))
    
    # Process with LLM (using AST CSV only for context)
    llm_records = []
    system_prompt = build_enhanced_system_prompt()
    
    # Configure LLM transformer
    transformer = LLMGraphTransformer(
        llm=llm,
        additional_instructions=system_prompt,
        allowed_nodes=['file', 'class', 'interface', 'method', 'variable', 'table', 'endpoint'],
        allowed_relationships=[
            ('file', 'declares', 'class'),
            ('file', 'declares', 'interface'),
            ('class', 'declares', 'method'),
            ('interface', 'declares', 'method'),
            ('class', 'declares', 'endpoint'),
            ('method', 'calls', 'method'),
            ('class', 'has_field', 'variable'),
            ('method', 'uses', 'variable'),
            ('class', 'uses', 'class'),
            ('interface', 'extends', 'interface'),
            ('class', 'extends', 'class'),
            ('class', 'implements', 'interface'),
            ('class', 'maps_to', 'table'),
            ('method', 'reads_from', 'table'),
            ('method', 'writes_to', 'table'),
        ],
        strict_mode=True,
        node_properties=False,
        relationship_properties=False,
    )
    
    # Process chunks with progress bar
    for chunk in tqdm(split_docs[:5], desc='Stage 4: LLM Processing (Sample)'):
        try:
            graph_docs = transformer.convert_to_graph_documents([chunk])
            for gd in graph_docs:
                for rel in gd.relationships:
                    source_node = clean_node_name(rel.source.id.strip())
                    target_node = clean_node_name(rel.target.id.strip())
                    source_type = rel.source.type.strip().upper()
                    target_type = rel.target.type.strip().upper()
                    relationship = rel.type.strip().upper()
                    
                    # Apply variable transformation if needed
                    if target_type == 'VARIABLE' and '.' not in target_node:
                        # Try to enhance variable name with context
                        if source_type == 'METHOD':
                            # Find the class containing this method
                            for class_name, info in class_registry.items():
                                if source_node.lower() in [m.lower() for m in info.get('methods', [])]:
                                    target_node = f"{class_name}.{source_node}.{target_node}"
                                    break
                    
                    if source_node and target_node:
                        llm_records.append({
                            'source_node': source_node,
                            'source_type': source_type,
                            'destination_node': target_node,
                            'destination_type': target_type,
                            'relationship': relationship,
                            'file_path': chunk.metadata.get('source')
                        })
        except Exception as e:
            continue
    
    return llm_records

# Execute Stage 4 (sample processing)
print("🔄 Starting LLM processing (sample for demonstration)...")
llm_records = process_with_llm()
df_llm = pd.DataFrame(llm_records)

print(f'✅ Stage 4 Complete: {len(df_llm)} LLM-enhanced relationships extracted')
print(f'📊 Enhanced Variables: {len(df_llm[df_llm["destination_type"] == "VARIABLE"])} variable relationships')

# ========== STAGE 4.5: DETAILED TRANSFORMATION TRACKING ==========

def extract_variable_transformations():
    """Extract detailed variable transformations like your example"""
    transformation_records = []
    
    for root, _, files in os.walk(BASE_PATH):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        source_code_str = f.read()
                    
                    class_name = file.replace('.java', '')
                    
                    # Extract method-level transformations
                    method_pattern = r'(public|private|protected)?\s*\w+\s+(\w+)\s*\([^)]*\)\s*\{([^}]+)\}'
                    method_matches = re.finditer(method_pattern, source_code_str, re.DOTALL)
                    
                    for method_match in method_matches:
                        method_name = method_match.group(2)
                        method_body = method_match.group(3)
                        
                        # Look for setter method calls (like setPatternCount)
                        setter_pattern = r'(\w+)\.(set\w+)\s*\(([^)]+)\)'
                        setter_matches = re.finditer(setter_pattern, method_body)
                        
                        for setter_match in setter_matches:
                            target_var = setter_match.group(1)
                            setter_method = setter_match.group(2)
                            expression = setter_match.group(3)
                            
                            # Create transformation record
                            transformation_records.append({
                                'source_node': f"{class_name}.{method_name}.temp",  # Example source
                                'destination_node': f"{class_name}.{method_name}.{target_var}",
                                'operation': setter_method,
                                'expression': expression.strip(),
                                'method': method_name,
                                'class': class_name,
                                'relationship': 'transforms_into'
                            })
                        
                        # Look for variable assignments (like temp = failureMetrics.get(0))
                        assignment_pattern = r'(\w+)\s*=\s*([^;]+);'
                        assignment_matches = re.finditer(assignment_pattern, method_body)
                        
                        for assign_match in assignment_matches:
                            var_name = assign_match.group(1)
                            assignment_expr = assign_match.group(2).strip()
                            
                            # Skip simple declarations
                            if 'new ' in assignment_expr or '.' in assignment_expr:
                                transformation_records.append({
                                    'source_node': assignment_expr,
                                    'destination_node': f"{class_name}.{method_name}.{var_name}",
                                    'operation': 'assignment',
                                    'expression': assignment_expr,
                                    'method': method_name,
                                    'class': class_name,
                                    'relationship': 'assigns_value'
                                })
                        
                        # Look for method calls that transform variables
                        method_call_pattern = r'(\w+)\s*=\s*(\w+)\.(\w+)\s*\([^)]*\)'
                        method_call_matches = re.finditer(method_call_pattern, method_body)
                        
                        for call_match in method_call_matches:
                            result_var = call_match.group(1)
                            source_var = call_match.group(2)
                            method_called = call_match.group(3)
                            
                            transformation_records.append({
                                'source_node': f"{class_name}.{method_name}.{source_var}",
                                'destination_node': f"{class_name}.{method_name}.{result_var}",
                                'operation': method_called,
                                'expression': f"{source_var}.{method_called}()",
                                'method': method_name,
                                'class': class_name,
                                'relationship': 'transforms_via_method'
                            })
                            
                except Exception as e:
                    print(f'⚠️ Error processing transformations in {file}: {e}')
                    continue
    
    return transformation_records

# Execute Stage 4.5
transformation_records = extract_variable_transformations()
df_transformations = pd.DataFrame(transformation_records)

print(f'✅ Stage 4.5 Complete: {len(transformation_records)} detailed transformations extracted')
print(f'📊 Transformation Types: {df_transformations["relationship"].value_counts().to_dict() if len(df_transformations) > 0 else "None"}')

# ========== STAGE 5: DATA CONSOLIDATION AND CLEANING ==========

def consolidate_and_clean_data():
    """Consolidate all data sources and apply cleaning"""
    
    # Combine all DataFrames
    all_dataframes = [df_project, df_folders, df_files, df_endpoints, df_data]
    if len(df_llm) > 0:
        all_dataframes.append(df_llm)
    
    df_combined = pd.concat(all_dataframes, ignore_index=True)
    
    # Clean and normalize
    for col in ["source_node", "destination_node"]:
        if col in df_combined.columns:
            df_combined[col] = df_combined[col].astype(str).apply(clean_node_name)
    
    # Normalize case for consistency
    for col in ["source_type", "destination_type", "relationship"]:
        if col in df_combined.columns:
            df_combined[col] = df_combined[col].astype(str).str.strip().str.upper()
    
    # Remove duplicates
    df_combined.drop_duplicates(
        subset=["source_node", "source_type", "destination_node", "destination_type", "relationship"],
        inplace=True
    )
    
    # Remove self-references
    df_combined = df_combined[
        ~((df_combined['source_node'] == df_combined['destination_node']) &
          (df_combined['source_type'] == df_combined['destination_type']))
    ]
    
    # Remove empty/null entries
    df_combined = df_combined[
        (df_combined['source_node'].notna()) & (df_combined['destination_node'].notna()) &
        (df_combined['source_node'] != '') & (df_combined['destination_node'] != '') &
        (df_combined['source_node'] != 'none') & (df_combined['destination_node'] != 'none')
    ]
    
    return df_combined

# Execute Stage 5
df_final = consolidate_and_clean_data()
df_final = normalize_nodes(df_final)

print(f'✅ Stage 5 Complete: {len(df_final)} consolidated relationships')
print(f'📊 Final Dataset: {df_final.shape[0]} rows, {df_final.shape[1]} columns')

# Save to CSV for analysis
df_final.to_csv('enhanced_java_lineage.csv', index=False)
print(f'💾 Data saved to enhanced_java_lineage.csv')

# ========== STAGE 6: NEO4J INTEGRATION ==========

def push_to_neo4j_enhanced(df_cleaned, graph):
    """Push enhanced data to Neo4j with proper variable transformations"""
    
    # Clear existing data
    print("🔄 Clearing existing Neo4j data...")
    graph.query("MATCH (n) DETACH DELETE n")
    
    # Insert data with progress tracking
    successful_inserts = 0
    failed_inserts = 0
    
    for idx, row in tqdm(df_cleaned.iterrows(), total=len(df_cleaned), desc="Neo4j Integration"):
        try:
            source_node = str(row["source_node"]).strip()
            dest_node = str(row["destination_node"]).strip()
            source_type = str(row["source_type"]).strip().upper()
            dest_type = str(row["destination_type"]).strip().upper()
            relationship = str(row["relationship"]).strip().upper()

            if not all([source_node, dest_node, source_type, dest_type, relationship]):
                failed_inserts += 1
                continue

            # Create nodes and relationships
            query = f"""
            MERGE (s:{source_type} {{name: $source_node}})
            MERGE (t:{dest_type} {{name: $destination_node}})
            MERGE (s)-[:{relationship}]->(t)
            """
            
            graph.query(query, {
                "source_node": source_node,
                "destination_node": dest_node
            })
            
            successful_inserts += 1
            
        except Exception as e:
            failed_inserts += 1
            continue

    print(f"✅ Neo4j Integration Complete:")
    print(f"   📈 Successful inserts: {successful_inserts}")
    print(f"   ⚠️ Failed inserts: {failed_inserts}")

def validate_neo4j_results():
    """Validate and display Neo4j results"""
    try:
        # Get basic statistics
        node_count = graph.query("MATCH (n) RETURN count(n) as count")[0]["count"]
        rel_count = graph.query("MATCH ()-[r]->() RETURN count(r) as count")[0]["count"]

        print(f"\n📊 Neo4j Database Statistics:")
        print(f"   🔵 Total Nodes: {node_count}")
        print(f"   🔗 Total Relationships: {rel_count}")
        
        # Show node type breakdown
        node_types = graph.query("MATCH (n) RETURN labels(n)[0] as type, count(n) as count ORDER BY count DESC")
        print('\n📋 Node Types:')
        for row in node_types:
            print(f'   {row["type"]}: {row["count"]}')
        
        # Show relationship type breakdown
        rel_types = graph.query("MATCH ()-[r]->() RETURN type(r) as type, count(r) as count ORDER BY count DESC")
        print('\n🔗 Relationship Types:')
        for row in rel_types:
            print(f'   {row["type"]}: {row["count"]}')
        
        # Show sample enhanced variables
        enhanced_vars = graph.query("""
            MATCH (v:VARIABLE) 
            WHERE v.name CONTAINS '.' 
            RETURN v.name as name 
            LIMIT 10
        """)
        
        if enhanced_vars:
            print('\n🔧 Sample Enhanced Variables (class.method.variable format):')
            for var in enhanced_vars:
                print(f'   {var["name"]}')
        
        # Show sample endpoints
        endpoints = graph.query("MATCH (n:ENDPOINT) RETURN n.name as name LIMIT 5")
        if endpoints:
            print('\n🌐 Sample API Endpoints:')
            for ep in endpoints:
                print(f'   {ep["name"]}')
        
        # Show sample data entities
        data_entities = graph.query("MATCH (n:DATA) RETURN n.name as name LIMIT 5")
        if data_entities:
            print('\n💾 Sample Data Entities:')
            for data in data_entities:
                print(f'   {data["name"]}')
                
    except Exception as e:
        print(f"⚠️ Error validating Neo4j results: {e}")

# Execute Stage 6
push_to_neo4j_enhanced(df_final, graph)
validate_neo4j_results()

# ========== STAGE 7: TRANSFORMATION DETAILS TO NEO4J ==========

def push_transformations_to_neo4j(transformation_records, graph):
    """Push detailed transformations to Neo4j like your example"""
    
    print("🔄 Adding detailed transformation relationships...")
    successful_transforms = 0
    failed_transforms = 0
    
    for rec in tqdm(transformation_records, desc="Transformation Details"):
        try:
            source_var = rec["source_node"]
            target_var = rec["destination_node"]
            expression = rec.get("expression", "")
            method_name = rec.get("method", "")
            class_name = rec.get("class", "")
            operation = rec.get("operation", "")

            # Build Cypher query exactly like your example
            query = """
            MERGE (c:CLASS {name: $class})
            MERGE (m:METHOD {name: $method})
            MERGE (c)-[:DECLARES]->(m)

            MERGE (v1:VARIABLE {name: $source_var})
            MERGE (v2:VARIABLE {name: $target_var})
            
            MERGE (m)-[:USES]->(v1)
            MERGE (m)-[:TRANSFORMS]->(v2)

            MERGE (op:OPERATION {name: $operation})
            MERGE (val:VALUE {expression: $expression})

            MERGE (v2)-[:UPDATED_BY]->(op)
            MERGE (op)-[:ASSIGNS_VALUE]->(val)
            """
            
            graph.query(query, {
                "class": class_name,
                "method": method_name,
                "source_var": source_var,
                "target_var": target_var,
                "operation": operation,
                "expression": expression
            })
            
            successful_transforms += 1
            
        except Exception as e:
            failed_transforms += 1
            print(f"⚠️ Failed transformation insert: {rec} => {e}")
    
    print(f"✅ Transformation Details Complete:")
    print(f"   📈 Successful transformations: {successful_transforms}")
    print(f"   ⚠️ Failed transformations: {failed_transforms}")

def validate_transformation_results():
    """Validate transformation results in Neo4j"""
    try:
        # Check for OPERATION nodes
        operations = graph.query("MATCH (n:OPERATION) RETURN count(n) as count")[0]["count"]
        print(f"\n🔧 Operations Created: {operations}")
        
        # Check for VALUE nodes
        values = graph.query("MATCH (n:VALUE) RETURN count(n) as count")[0]["count"]
        print(f"💎 Values Created: {values}")
        
        # Show sample transformation chain
        transformation_chain = graph.query("""
            MATCH (m:METHOD)-[:USES]->(v1:VARIABLE)
            MATCH (m)-[:TRANSFORMS]->(v2:VARIABLE)
            MATCH (v2)-[:UPDATED_BY]->(op:OPERATION)
            MATCH (op)-[:ASSIGNS_VALUE]->(val:VALUE)
            RETURN m.name as method, v1.name as source_var, v2.name as target_var, 
                   op.name as operation, val.expression as value
            LIMIT 5
        """)
        
        if transformation_chain:
            print('\n🔄 Sample Transformation Chains:')
            for chain in transformation_chain:
                print(f'   {chain["method"]}: {chain["source_var"]} --[{chain["operation"]}]--> {chain["target_var"]} = {chain["value"]}')
        
        # Show TRANSFORMS relationships
        transforms = graph.query("""
            MATCH (v1:VARIABLE)-[:TRANSFORMS]->(v2:VARIABLE)
            RETURN v1.name as source, v2.name as target
            LIMIT 10
        """)
        
        if transforms:
            print('\n🔀 Variable Transformations:')
            for transform in transforms:
                print(f'   {transform["source"]} → {transform["target"]}')
                
    except Exception as e:
        print(f"⚠️ Error validating transformations: {e}")

# Execute Stage 7 if we have transformation records
if len(transformation_records) > 0:
    push_transformations_to_neo4j(transformation_records, graph)
    validate_transformation_results()
else:
    print("⚠️ No transformation records found to process")