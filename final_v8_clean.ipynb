# ========== IMPORTS AND SETUP ==========
import os
from pathlib import Path
from tqdm import tqdm
import pandas as pd
import re
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph

# ========== CONFIGURATION ==========
BASE_PATH = Path(r'C:/Shaik/sample/OneInsights')
NEO4J_URI = 'bolt://localhost:7687'
NEO4J_USER = 'neo4j'
NEO4J_PASSWORD = 'Test@7889'
NEO4J_DB = 'oneinsights-v8'
GOOGLE_API_KEY = 'AIzaSyAeSuntl3dxGqQhwxRG_jom1V_EjxEPSwc'

# ========== INITIALIZE COMPONENTS ==========
try:
    graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
    print('✅ Neo4j connection established')
except Exception as e:
    print(f'❌ Neo4j connection failed: {e}')
    graph = None

JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

llm = ChatGoogleGenerativeAI(
    model='gemini-2.0-flash-exp',
    temperature=0,
    google_api_key=GOOGLE_API_KEY
)

# ========== ENHANCED FILTERING ==========
NOISE_VARIABLES = {
    # Loop variables
    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',
    # Common temporary variables
    'temp', 'tmp', 'obj', 'item', 'elem', 'node', 'val', 'value',
    # Common short variables
    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',
    # Reserved keywords
    'this', 'super', 'null', 'true', 'false', 'void', 'return',
    # Meaningless short names
    'it', 'ex', 'e1', 'e2', 'o1', 'o2'
}

APPLICATIONS = {
    'ServiceBolt': 'REST API Service Layer',
    'UnifiedBolt': 'Core Business Logic and Data Layer'
}

def is_meaningful_variable(var_name):
    '''Filter out noise variables and keep only meaningful ones'''
    if not var_name or len(var_name) < 2:
        return False
    
    if var_name.lower() in NOISE_VARIABLES:
        return False
    
    if len(var_name) > 3:
        return True
    
    meaningful_short = {'id', 'url', 'api', 'key', 'dto', 'dao', 'req', 'res', 'ctx'}
    return var_name.lower() in meaningful_short

def detect_application(file_path):
    '''Detect which application a file belongs to'''
    path_str = str(file_path).replace('\\', '/')
    if 'ServiceBolt' in path_str:
        return 'ServiceBolt'
    elif 'UnifiedBolt' in path_str:
        return 'UnifiedBolt'
    return 'Unknown'

print('🚀 Setup complete! Ready for analysis...')

# ========== STAGE 1: FOLDER + FILE HIERARCHY ==========

def extract_enhanced_folder_file_hierarchy(base_path):
    '''Extract folder and file relationships with application context'''
    folder_records, file_records, app_records = [], [], []
    base_path = os.path.abspath(base_path)
    base_folder_name = os.path.basename(base_path)
    processed_folders = set()
    detected_apps = set()

    for root, dirs, files in os.walk(base_path):
        rel_root = os.path.relpath(root, base_path)
        parent_folder = base_folder_name if rel_root == '.' else os.path.dirname(rel_root) or base_folder_name
        current_folder = base_folder_name if rel_root == '.' else os.path.basename(rel_root)
        
        app_name = detect_application(root)
        if app_name != 'Unknown':
            detected_apps.add(app_name)

        folder_key = f'{parent_folder}->{current_folder}'
        if folder_key not in processed_folders and parent_folder != current_folder:
            folder_records.append({
                'source_node': parent_folder,
                'source_type': 'folder',
                'destination_node': current_folder,
                'destination_type': 'folder',
                'relationship': 'contains',
                'file_path': None,
                'application': app_name
            })
            processed_folders.add(folder_key)

        for f in files:
            if f.endswith('.java'):
                file_rel_path = os.path.relpath(os.path.join(root, f), base_path)
                file_records.append({
                    'source_node': current_folder,
                    'source_type': 'folder',
                    'destination_node': f,
                    'destination_type': 'file',
                    'relationship': 'contains',
                    'file_path': file_rel_path,
                    'application': app_name
                })
    
    # Create application nodes
    for app in detected_apps:
        app_records.append({
            'source_node': base_folder_name,
            'source_type': 'project',
            'destination_node': app,
            'destination_type': 'application',
            'relationship': 'contains',
            'file_path': None,
            'application': app
        })
    
    return folder_records, file_records, app_records

print('📁 Extracting folder and file hierarchy...')
folder_records, file_records, app_records = extract_enhanced_folder_file_hierarchy(BASE_PATH)
df_folders = pd.DataFrame(folder_records)
df_files = pd.DataFrame(file_records)
df_apps = pd.DataFrame(app_records)

print(f'✅ Stage 1 Complete: {len(df_folders)} folder relationships, {len(df_files)} file relationships, {len(df_apps)} applications')

# ========== UTILITY FUNCTIONS ==========

def read_source_code(file_path):
    '''Read source code file'''
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read().encode('utf-8')

def extract_package_and_imports(source_code_str):
    '''Extract package name and imports from Java source'''
    package_pattern = r'package\s+([\w\.]+);'
    import_pattern = r'import\s+([\w\.]+);'
    package_match = re.search(package_pattern, source_code_str)
    package_name = package_match.group(1) if package_match else None
    import_matches = re.findall(import_pattern, source_code_str)
    return package_name, import_matches

def extract_api_endpoints(source_code_str):
    '''Extract REST API endpoints from Spring annotations'''
    endpoints = []
    mapping_patterns = {
        'RequestMapping': [
            r'@RequestMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']',
            r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@RequestMapping\s*\(\s*path\s*=\s*["\']([^"\']+)["\']'
        ],
        'GetMapping': [
            r'@GetMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@GetMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
        ],
        'PostMapping': [
            r'@PostMapping\s*\(\s*["\']([^"\']+)["\']',
            r'@PostMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
        ],
        'PutMapping': [
            r'@PutMapping\s*\(\s*["\']([^"\']+)["\']'
        ],
        'DeleteMapping': [
            r'@DeleteMapping\s*\(\s*["\']([^"\']+)["\']'
        ]
    }
    
    for mapping_type, patterns in mapping_patterns.items():
        for pattern in patterns:
            matches = re.findall(pattern, source_code_str, re.MULTILINE | re.DOTALL)
            for match in matches:
                if match.strip():
                    endpoints.append({
                        'type': mapping_type,
                        'path': match.strip(),
                        'method': mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'
                    })
    
    return endpoints

def extract_database_entities(source_code_str):
    '''Extract database entities from JPA annotations'''
    entities = []
    
    # Entity detection
    entity_patterns = [
        r'@Entity\s*(?:\([^)]*\))?',
        r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']'
    ]
    
    for pattern in entity_patterns:
        if re.search(pattern, source_code_str, re.MULTILINE):
            table_matches = re.findall(r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']', source_code_str)
            for table_name in table_matches:
                if table_name.strip():
                    entities.append({
                        'type': 'table',
                        'name': table_name.strip()
                    })
    
    # Repository pattern detection
    repository_pattern = r'interface\s+(\w+)\s+extends\s+.*Repository'
    repo_matches = re.findall(repository_pattern, source_code_str)
    for repo_name in repo_matches:
        entity_name = repo_name.replace('Repository', '').replace('Rep', '')
        if entity_name:
            entities.append({
                'type': 'table',
                'name': entity_name.lower()
            })
    
    return entities

def extract_class_relationships(source_code_str):
    '''Extract class inheritance relationships'''
    relationships = []
    
    # Class extends
    class_extends_pattern = r'class\s+(\w+)\s+extends\s+([\w<>]+)'
    class_matches = re.findall(class_extends_pattern, source_code_str)
    for child_class, parent_class in class_matches:
        parent_class = re.sub(r'<.*?>', '', parent_class).strip()
        if parent_class:
            relationships.append({
                'child_class': child_class,
                'parent_class': parent_class,
                'relationship_type': 'extends'
            })
    
    # Class implements (minimal as per user preference)
    implements_pattern = r'class\s+(\w+)(?:\s+extends\s+\w+)?\s+implements\s+([\w<>,\s]+)'
    impl_matches = re.findall(implements_pattern, source_code_str)
    for class_name, implements_clause in impl_matches:
        interfaces = [part.strip().split('<')[0].strip() for part in implements_clause.split(',')]
        for interface in interfaces:
            if interface and len(interface) > 3:
                relationships.append({
                    'child_class': class_name,
                    'parent_class': interface,
                    'relationship_type': 'implements'
                })
    
    return relationships

print('🔧 Utility functions loaded')

# ========== DATA LINEAGE PATTERNS ==========

def extract_data_lineage_patterns(source_code_str):
    '''Extract data lineage patterns from Java code'''
    lineage_records = []
    
    # 1. SQL Query patterns - Data Sources
    sql_patterns = [
        r'@Query\s*\(\s*["\']([^"\']*(SELECT|INSERT|UPDATE|DELETE)[^"\']*)["\']',
        r'@Query\s*\(\s*value\s*=\s*["\']([^"\']*(SELECT|INSERT|UPDATE|DELETE)[^"\']*)["\']',
        r'@Query\s*\(\s*nativeQuery\s*=\s*true\s*,\s*value\s*=\s*["\']([^"\']*(SELECT|INSERT|UPDATE|DELETE)[^"\']*)["\']'
    ]
    
    for pattern in sql_patterns:
        matches = re.findall(pattern, source_code_str, re.MULTILINE | re.IGNORECASE | re.DOTALL)
        for match in matches:
            if isinstance(match, tuple):
                query = match[0]
                operation = match[1].upper()
            else:
                query = match
                operation = 'SELECT'
            
            # Extract table names from SQL
            table_patterns = [
                r'FROM\s+(\w+)',
                r'JOIN\s+(\w+)',
                r'UPDATE\s+(\w+)',
                r'INSERT\s+INTO\s+(\w+)',
                r'DELETE\s+FROM\s+(\w+)'
            ]
            
            for table_pattern in table_patterns:
                table_matches = re.findall(table_pattern, query, re.IGNORECASE)
                for table_name in table_matches:
                    lineage_records.append({
                        'source_type': 'table',
                        'source_name': table_name.lower(),
                        'target_type': 'query',
                        'target_name': f'{operation}_query',
                        'operation': operation,
                        'lineage_type': 'data_source'
                    })
    
    # 2. Data Transformation patterns
    transformation_patterns = [
        r'(\w+)\s*=\s*(\w+)\.stream\s*\(\s*\)\.map\s*\(',
        r'(\w+)\s*=\s*(\w+)\.stream\s*\(\s*\)\.filter\s*\(',
        r'(\w+)\s*=\s*(\w+)\.stream\s*\(\s*\)\.collect\s*\(',
        r'(\w+)\s*=\s*convert\w*\s*\(\s*(\w+)',
        r'(\w+)\s*=\s*transform\w*\s*\(\s*(\w+)',
        r'(\w+)\s*=\s*\w*Mapper\s*\.\s*\w+\s*\(\s*(\w+)'
    ]
    
    for pattern in transformation_patterns:
        matches = re.findall(pattern, source_code_str, re.MULTILINE)
        for match in matches:
            if len(match) >= 2:
                target_var = match[0]
                source_var = match[1]
                
                if is_meaningful_variable(target_var) and is_meaningful_variable(source_var):
                    lineage_records.append({
                        'source_type': 'data',
                        'source_name': source_var,
                        'target_type': 'data',
                        'target_name': target_var,
                        'operation': 'transform',
                        'lineage_type': 'data_transformation'
                    })
    
    # 3. API Data Flow patterns
    api_patterns = [
        r'(\w+)\s*=\s*restTemplate\s*\.\s*(get|post|put|delete)\w*\s*\(',
        r'(\w+)\s*=\s*(\w+Service)\s*\.\s*(\w+)\s*\(',
        r'(\w+)\s*=\s*(\w+Repository)\s*\.\s*(find|save|delete)\w*\s*\('
    ]
    
    for pattern in api_patterns:
        matches = re.findall(pattern, source_code_str, re.MULTILINE)
        for match in matches:
            if len(match) >= 2:
                result_var = match[0]
                service_name = match[1] if len(match) >= 2 else 'external_service'
                operation = match[2] if len(match) >= 3 else 'call'
                
                if is_meaningful_variable(result_var):
                    lineage_records.append({
                        'source_type': 'service',
                        'source_name': service_name,
                        'target_type': 'data',
                        'target_name': result_var,
                        'operation': operation,
                        'lineage_type': 'service_data_flow'
                    })
    
    return lineage_records

def build_data_lineage_graph(source_code_str, class_name, app_name):
    '''Build comprehensive data lineage graph for a class'''
    all_lineage = extract_data_lineage_patterns(source_code_str)
    
    # Add class and application context
    for record in all_lineage:
        record['class_name'] = class_name
        record['application'] = app_name
    
    return all_lineage

print('📊 Data lineage patterns loaded')

# ========== CLASS REGISTRY WITH DATA LINEAGE ==========

def build_enhanced_class_registry():
    '''Build comprehensive class registry with all metadata'''
    class_registry = {}
    inter_app_calls = []
    
    print('🔍 Building class registry...')
    java_files = list(BASE_PATH.rglob('*.java'))
    
    for file_path in tqdm(java_files, desc='Processing files'):
        app_name = detect_application(file_path)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code_str = f.read()
            
            package_name, imports = extract_package_and_imports(source_code_str)
            endpoints = extract_api_endpoints(source_code_str)
            db_entities = extract_database_entities(source_code_str)
            class_relationships = extract_class_relationships(source_code_str)
            
            class_name = file_path.stem
            fqcn = f'{package_name}.{class_name}' if package_name else class_name
            
            # Extract data lineage
            data_lineage = build_data_lineage_graph(source_code_str, class_name, app_name)
            
            # Detect inter-application dependencies
            for imp in imports:
                if app_name == 'ServiceBolt' and 'UnifiedBolt' in imp:
                    inter_app_calls.append({
                        'from_app': 'ServiceBolt',
                        'to_app': 'UnifiedBolt',
                        'from_class': class_name,
                        'imported_class': imp.split('.')[-1],
                        'import_type': 'dependency'
                    })
                elif app_name == 'UnifiedBolt' and 'ServiceBolt' in imp:
                    inter_app_calls.append({
                        'from_app': 'UnifiedBolt',
                        'to_app': 'ServiceBolt',
                        'from_class': class_name,
                        'imported_class': imp.split('.')[-1],
                        'import_type': 'dependency'
                    })
            
            class_registry[class_name] = {
                'fqcn': fqcn,
                'package': package_name,
                'file_path': str(file_path),
                'application': app_name,
                'imports': imports,
                'endpoints': endpoints,
                'db_entities': db_entities,
                'class_relationships': class_relationships,
                'data_lineage': data_lineage
            }
            
        except Exception as e:
            print(f'❌ Error processing {file_path.name}: {e}')
            continue
    
    return class_registry, inter_app_calls

class_registry, inter_app_calls = build_enhanced_class_registry()

# Summary statistics
total_endpoints = sum(len(info.get('endpoints', [])) for info in class_registry.values())
total_db_entities = sum(len(info.get('db_entities', [])) for info in class_registry.values())
total_data_lineage = sum(len(info.get('data_lineage', [])) for info in class_registry.values())

print(f'✅ Class registry built:')
print(f'   📁 {len(class_registry)} classes processed')
print(f'   🌐 {total_endpoints} API endpoints found')
print(f'   🗄️ {total_db_entities} database entities found')
print(f'   📊 {total_data_lineage} data lineage patterns found')
print(f'   🔗 {len(inter_app_calls)} inter-application dependencies')

# ========== ENHANCED AST EXTRACTION ==========

def extract_variable_transformations(source_code_str):
    '''Extract variable transformations like a + b = c'''
    transformations = []
    
    assignment_patterns = [
        r'(\w+)\s*=\s*(\w+)\s*[+\-*/]\s*(\w+)',  # binary operations
        r'(\w+)\s*=\s*(\w+)\s*\.\s*(\w+)\s*\(',  # method calls
        r'(\w+)\s*=\s*new\s+(\w+)\s*\(',  # object creation
    ]
    
    for pattern in assignment_patterns:
        matches = re.findall(pattern, source_code_str, re.MULTILINE)
        for match in matches:
            if len(match) >= 3:
                result_var = match[0]
                input_vars = [match[1], match[2]]
                
                if (is_meaningful_variable(result_var) and 
                    all(is_meaningful_variable(var) for var in input_vars)):
                    transformations.append({
                        'result': result_var,
                        'inputs': input_vars,
                        'operation': 'transform'
                    })
    
    return transformations

def extract_enhanced_ast_structure(file_path):
    '''Extract AST structure with enhanced filtering'''
    records = []
    source_code = read_source_code(file_path)
    source_code_str = source_code.decode('utf-8')
    tree = parser.parse(source_code)
    root_node = tree.root_node
    file_name = os.path.basename(file_path)
    app_name = detect_application(file_path)
    
    transformations = extract_variable_transformations(source_code_str)

    def clean_node_name(name):
        '''Clean node names'''
        if not name:
            return name
        
        prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']
        for prefix in prefixes_to_remove:
            if name.lower().startswith(prefix):
                name = name[len(prefix):]
        
        name = re.sub(r'\.(java|class)$', '', name, flags=re.IGNORECASE)
        return name.strip()

    def traverse(node, parent_type=None, parent_name=None, context='global'):
        # Handle class declarations
        if node.type == 'class_declaration':
            class_name = None
            for child in node.children:
                if child.type == 'identifier':
                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    
                    # File -> Class relationship
                    records.append({
                        'source_node': file_name,
                        'source_type': 'file',
                        'destination_node': class_name,
                        'destination_type': 'class',
                        'relationship': 'declares',
                        'file_path': str(file_path),
                        'application': app_name
                    })
                    
                    # Add metadata from registry
                    class_info = class_registry.get(class_name, {})
                    
                    # API endpoints
                    for ep in class_info.get('endpoints', []):
                        endpoint_name = f"{ep['method']} {ep['path']}"
                        records.append({
                            'source_node': class_name,
                            'source_type': 'class',
                            'destination_node': endpoint_name,
                            'destination_type': 'endpoint',
                            'relationship': 'exposes',
                            'file_path': str(file_path),
                            'application': app_name
                        })
                    
                    # Database entities
                    for entity in class_info.get('db_entities', []):
                        records.append({
                            'source_node': class_name,
                            'source_type': 'class',
                            'destination_node': entity['name'],
                            'destination_type': 'table',
                            'relationship': 'maps_to',
                            'file_path': str(file_path),
                            'application': app_name
                        })
                    
                    # Class relationships
                    for rel in class_info.get('class_relationships', []):
                        if rel['child_class'] == class_name:
                            records.append({
                                'source_node': class_name,
                                'source_type': 'class',
                                'destination_node': rel['parent_class'],
                                'destination_type': 'class',
                                'relationship': rel['relationship_type'],
                                'file_path': str(file_path),
                                'application': app_name
                            })
                    
                    # Data lineage
                    for lineage in class_info.get('data_lineage', []):
                        records.append({
                            'source_node': lineage['source_name'],
                            'source_type': lineage['source_type'],
                            'destination_node': lineage['target_name'],
                            'destination_type': lineage['target_type'],
                            'relationship': f"data_{lineage['operation']}",
                            'file_path': str(file_path),
                            'application': app_name
                        })
                    break
            
            # Traverse children with class context
            for child in node.children:
                traverse(child, 'class', class_name, 'class')
                
        # Handle method declarations
        elif node.type == 'method_declaration':
            method_name = None
            for child in node.children:
                if child.type == 'identifier':
                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    
                    if parent_name and parent_type in ['class', 'interface']:
                        records.append({
                            'source_node': parent_name,
                            'source_type': parent_type,
                            'destination_node': method_name,
                            'destination_type': 'method',
                            'relationship': 'declares',
                            'file_path': str(file_path),
                            'application': app_name
                        })
                    break
            
            for child in node.children:
                traverse(child, 'method', method_name, 'method')
                
        # Handle field declarations with filtering
        elif node.type == 'field_declaration':
            for child in node.children:
                if child.type == 'variable_declarator':
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))
                            
                            if is_meaningful_variable(field_name) and parent_name and parent_type == 'class':
                                records.append({
                                    'source_node': parent_name,
                                    'source_type': parent_type,
                                    'destination_node': field_name,
                                    'destination_type': 'variable',
                                    'relationship': 'has_field',
                                    'file_path': str(file_path),
                                    'application': app_name
                                })
        else:
            # Continue traversing
            for child in node.children:
                traverse(child, parent_type, parent_name, context)

    traverse(root_node)
    
    # Add transformation relationships
    for transform in transformations:
        for input_var in transform['inputs']:
            records.append({
                'source_node': input_var,
                'source_type': 'variable',
                'destination_node': transform['result'],
                'destination_type': 'variable',
                'relationship': 'transforms_to',
                'file_path': str(file_path),
                'application': app_name
            })
    
    return records

# Execute AST extraction
print('🌳 Extracting AST structures...')
ast_records = []
java_files = list(BASE_PATH.rglob('*.java'))

for file_path in tqdm(java_files, desc='AST Processing'):
    try:
        ast_records.extend(extract_enhanced_ast_structure(file_path))
    except Exception as e:
        print(f'❌ Error processing {file_path.name}: {e}')
        continue

df_ast = pd.DataFrame(ast_records)
print(f'✅ AST extraction complete: {len(df_ast)} relationships extracted')
if len(df_ast) > 0:
    print(f'   Applications: {df_ast["application"].value_counts().to_dict()}')

# ========== LLM EXTRACTION WITH PROPER CHUNKING ==========

def build_enhanced_system_prompt_v8(file_path, ast_df, class_registry, inter_app_calls):
    '''Build enhanced system prompt with context'''
    ast_subset = ast_df[ast_df['file_path'] == str(file_path)] if len(ast_df) > 0 else pd.DataFrame()
    app_name = detect_application(file_path)
    
    ast_context = ''
    for _, row in ast_subset.iterrows():
        ast_context += f"{row['source_type']}:{row['source_node']} -[{row['relationship']}]-> {row['destination_type']}:{row['destination_node']}\n"
    
    registry_context = f'Current Application: {app_name}\n\nKnown Classes:\n'
    for class_name, info in list(class_registry.items())[:10]:  # Limit context
        registry_context += f'- {class_name} (App: {info.get("application", "Unknown")})\n'
        if len(info.get('endpoints', [])) > 0:
            registry_context += f'  * {len(info["endpoints"])} API endpoint(s)\n'
        if len(info.get('db_entities', [])) > 0:
            registry_context += f'  * {len(info["db_entities"])} DB entity/entities\n'
    
    inter_app_context = '\nInter-Application Dependencies:\n'
    for call in inter_app_calls[:5]:  # Limit context
        inter_app_context += f'- {call["from_app"]}.{call["from_class"]} -> {call["to_app"]}.{call["imported_class"]}\n'
    
    prompt = f"""
You are a Java code lineage extraction engine for multi-application systems. Extract relationships with STRICT focus on:

CONTEXT:
{registry_context}
{inter_app_context}

AST RELATIONSHIPS (FOLLOW THESE PATTERNS EXACTLY):
{ast_context}

CRITICAL RULES - FOLLOW EXACTLY:
1. Use SIMPLE names only (remove prefixes like "method:", "class:", etc.)
2. MANDATORY RELATIONSHIP DIRECTIONS (DO NOT REVERSE):
   - file -[declares]-> class
   - class -[declares]-> method  
   - class -[has_field]-> variable (ONLY meaningful variables, NO loop vars like i,j,k)
   - method -[uses]-> variable (ONLY meaningful variables)
   - method -[returns]-> variable
   - variable -[transforms_to]-> variable (for a+b=c patterns)
   - class -[exposes]-> endpoint
   - class -[maps_to]-> table
   - class -[extends]-> class
   - application -[contains]-> class
3. VARIABLE FILTERING: Ignore loop variables (i,j,k), temp variables, single letters
4. TRANSFORMATION DETECTION: Extract patterns like result = input1 + input2
5. Extract REST API endpoints as 'endpoint' nodes (GET /api/users, POST /api/data)
6. Extract database tables from @Entity, @Table, @Query annotations
7. Focus on class extends relationships (not interfaces)
8. Track inter-application dependencies
9. NEVER create reverse relationships (method->class, variable->method, etc.)
10. Follow the AST RELATIONSHIPS above for correct structure

Extract triples in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the triples, no explanations.
"""
    return prompt

def extract_llm_relationships_v8():
    '''Extract relationships using LLM with proper chunking'''
    llm_records = []
    
    # Get all Java files
    java_files = list(BASE_PATH.rglob('*.java'))
    print(f'🤖 Processing {len(java_files)} files with LLM...')
    
    # Process files in chunks to handle all files
    chunk_size = 50  # Process 50 files at a time
    file_chunks = [java_files[i:i + chunk_size] for i in range(0, len(java_files), chunk_size)]
    
    for chunk_idx, file_chunk in enumerate(file_chunks):
        print(f'\n📦 Processing chunk {chunk_idx + 1}/{len(file_chunks)} ({len(file_chunk)} files)...')
        
        for file_path in tqdm(file_chunk, desc=f'Chunk {chunk_idx + 1}'):
            try:
                # Check file size
                file_size = os.path.getsize(file_path)
                if file_size > 50000:  # Skip very large files (>50KB)
                    print(f'⚠️ Skipping large file: {file_path.name} ({file_size} bytes)')
                    continue
                
                # Read and chunk the file
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Split into manageable chunks
                text_splitter = RecursiveCharacterTextSplitter(
                    chunk_size=8000,
                    chunk_overlap=200,
                    length_function=len,
                    separators=["\n\n", "\n", " ", ""]
                )
                
                chunks = text_splitter.split_text(content)
                
                for chunk_text in chunks:
                    if len(chunk_text.strip()) < 100:  # Skip tiny chunks
                        continue
                    
                    # Build prompt
                    system_prompt = build_enhanced_system_prompt_v8(file_path, df_ast, class_registry, inter_app_calls)
                    
                    # Create document
                    from langchain.schema import Document
                    doc = Document(page_content=chunk_text, metadata={'source': str(file_path)})
                    
                    # Transform with LLM
                    llm_transformer = LLMGraphTransformer(
                        llm=llm,
                        prompt=system_prompt
                    )
                    
                    try:
                        graph_documents = llm_transformer.convert_to_graph_documents([doc])
                        
                        for graph_doc in graph_documents:
                            for relationship in graph_doc.relationships:
                                llm_records.append({
                                    'source_node': relationship.source.id,
                                    'source_type': relationship.source.type,
                                    'destination_node': relationship.target.id,
                                    'destination_type': relationship.target.type,
                                    'relationship': relationship.type,
                                    'file_path': str(file_path),
                                    'application': detect_application(file_path)
                                })
                    
                    except Exception as e:
                        print(f'⚠️ LLM processing failed for chunk in {file_path.name}: {e}')
                        continue
            
            except Exception as e:
                print(f'❌ Error processing {file_path.name}: {e}')
                continue
    
    return llm_records

# Execute LLM extraction
print('\n🤖 Starting LLM extraction...')
llm_records = extract_llm_relationships_v8()
df_llm = pd.DataFrame(llm_records)

print(f'✅ LLM extraction complete: {len(df_llm)} relationships extracted')
if len(df_llm) > 0:
    print(f'   Relationship types: {df_llm["relationship"].value_counts().head().to_dict()}')

# ========== DATA COMBINATION AND NEO4J LOADING ==========

def combine_clean_enhanced_data():
    '''Combine all data sources with intelligent filtering'''
    all_records = []
    
    # Add folder and file relationships
    all_records.extend(folder_records)
    all_records.extend(file_records)
    all_records.extend(app_records)
    print(f'📁 Added {len(folder_records + file_records + app_records)} folder/file relationships')
    
    # Add clean AST relationships (filter noise)
    clean_ast_records = []
    for record in ast_records:
        # Include structural relationships and data lineage
        if (record['relationship'] in ['declares', 'exposes', 'maps_to', 'extends', 'implements'] or 
            record['relationship'].startswith('data_')):
            clean_ast_records.append(record)
        # Include meaningful field relationships
        elif (record['relationship'] == 'has_field' and 
              is_meaningful_variable(record['destination_node'])):
            clean_ast_records.append(record)
        # Include meaningful transformations
        elif record['relationship'] == 'transforms_to':
            clean_ast_records.append(record)
    
    all_records.extend(clean_ast_records)
    print(f'🌳 Added {len(clean_ast_records)} clean AST relationships (filtered from {len(ast_records)})')
    
    # Add filtered LLM relationships
    if len(df_llm) > 0:
        llm_records_clean = []
        for _, row in df_llm.iterrows():
            # Filter meaningful LLM relationships
            if (row['relationship'].upper() in ['DECLARES', 'EXTENDS', 'IMPLEMENTS', 'USES', 'CALLS', 'RETURNS'] and
                is_meaningful_variable(row['source_node']) and 
                is_meaningful_variable(row['destination_node'])):
                llm_records_clean.append(row.to_dict())
        
        all_records.extend(llm_records_clean)
        print(f'🤖 Added {len(llm_records_clean)} filtered LLM relationships (from {len(df_llm)})')
    
    # Add inter-application relationships
    for call in inter_app_calls:
        all_records.append({
            'source_node': call['from_class'],
            'source_type': 'class',
            'destination_node': call['imported_class'],
            'destination_type': 'class',
            'relationship': 'depends_on',
            'file_path': None,
            'application': call['from_app']
        })
    
    print(f'🔗 Added {len(inter_app_calls)} inter-application dependencies')
    
    return pd.DataFrame(all_records)

def load_to_neo4j(df_final):
    '''Load data to Neo4j with error handling'''
    if graph is None:
        print('❌ Neo4j connection not available')
        return
    
    try:
        # Clear existing data
        print('🧹 Clearing existing data...')
        graph.query('MATCH (n) DETACH DELETE n')
        
        # Create nodes and relationships
        print('📊 Loading nodes and relationships...')
        
        # Group by relationship type for efficient loading
        relationship_groups = df_final.groupby('relationship')
        
        for rel_type, group in tqdm(relationship_groups, desc='Loading relationships'):
            for _, row in group.iterrows():
                try:
                    # Create nodes and relationship
                    cypher = f"""
                    MERGE (source:{row['source_type'].title()} {{name: $source_name, application: $app}})
                    MERGE (target:{row['destination_type'].title()} {{name: $target_name, application: $app}})
                    MERGE (source)-[:{rel_type.upper().replace(' ', '_')}]->(target)
                    """
                    
                    graph.query(cypher, {
                        'source_name': str(row['source_node']),
                        'target_name': str(row['destination_node']),
                        'app': str(row['application'])
                    })
                
                except Exception as e:
                    print(f'⚠️ Error loading relationship: {e}')
                    continue
        
        # Create indexes for performance
        indexes = [
            'CREATE INDEX IF NOT EXISTS FOR (n:Class) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Method) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:File) ON (n.name)',
            'CREATE INDEX IF NOT EXISTS FOR (n:Application) ON (n.name)'
        ]
        
        for index in indexes:
            try:
                graph.query(index)
            except:
                pass
        
        print('✅ Data successfully loaded to Neo4j')
        
        # Get statistics
        stats = graph.query("""
        MATCH (n)
        RETURN labels(n)[0] as node_type, count(*) as count
        ORDER BY count DESC
        """)
        
        print('\n📊 Neo4j Statistics:')
        for stat in stats:
            print(f'   {stat["node_type"]}: {stat["count"]} nodes')
    
    except Exception as e:
        print(f'❌ Error loading to Neo4j: {e}')

# Execute data combination and loading
print('\n🔄 Combining all data sources...')
df_final = combine_clean_enhanced_data()

print(f'\n✅ Final dataset created:')
print(f'   📊 Total relationships: {len(df_final)}')
print(f'   🏢 Applications: {df_final["application"].value_counts().to_dict()}')
print(f'   🔗 Relationship types: {len(df_final["relationship"].unique())}')

# Load to Neo4j
print('\n🚀 Loading to Neo4j...')
load_to_neo4j(df_final)

# ========== COMPREHENSIVE ANALYSIS ==========

def analyze_enhanced_codebase():
    '''Perform comprehensive analysis of the codebase'''
    if graph is None:
        print('❌ Neo4j connection not available for analysis')
        return
    
    print('\n🔍 COMPREHENSIVE CODEBASE ANALYSIS')
    print('=' * 60)
    
    # Application overview
    app_stats = graph.query("""
    MATCH (app:Application)
    OPTIONAL MATCH (app)-[:CONTAINS]->(class:Class)
    RETURN app.name as application,
           count(class) as class_count
    ORDER BY class_count DESC
    """)
    
    print('\n🏢 Application Overview:')
    for record in app_stats:
        print(f'   {record["application"]}: {record["class_count"]} classes')
    
    # API endpoints analysis
    endpoints = graph.query("""
    MATCH (class:Class)-[:EXPOSES]->(endpoint:Endpoint)
    RETURN class.application as app,
           class.name as controller,
           endpoint.name as endpoint
    ORDER BY app, controller
    LIMIT 20
    """)
    
    print('\n🌐 API Endpoints:')
    current_app = None
    for record in endpoints:
        if record['app'] != current_app:
            current_app = record['app']
            print(f'\n   {current_app}:')
        print(f'     {record["endpoint"]} (in {record["controller"]})')
    
    # Database entities analysis
    db_entities = graph.query("""
    MATCH (class:Class)-[:MAPS_TO]->(table:Table)
    RETURN class.application as app,
           class.name as entity_class,
           table.name as table_name
    ORDER BY app, entity_class
    """)
    
    print('\n🗄️ Database Entities:')
    current_app = None
    for record in db_entities:
        if record['app'] != current_app:
            current_app = record['app']
            print(f'\n   {current_app}:')
        print(f'     {record["entity_class"]} -> {record["table_name"]}')
    
    # Inter-application dependencies
    inter_deps = graph.query("""
    MATCH (source:Class)-[r:DEPENDS_ON]->(target:Class)
    WHERE source.application <> target.application
    RETURN source.application as from_app,
           target.application as to_app,
           count(*) as dependency_count
    ORDER BY dependency_count DESC
    """)
    
    print('\n🔗 Inter-Application Dependencies:')
    for record in inter_deps:
        print(f'   {record["from_app"]} -> {record["to_app"]}: {record["dependency_count"]} dependencies')
    
    # Class inheritance analysis
    inheritance = graph.query("""
    MATCH (child:Class)-[r:EXTENDS]->(parent:Class)
    RETURN child.application as app,
           child.name as child_class,
           parent.name as parent_class
    ORDER BY app, child_class
    """)
    
    print('\n🏗️ Class Inheritance:')
    for record in inheritance:
        print(f'   {record["app"]}: {record["child_class"]} extends {record["parent_class"]}')
    
    # Data lineage analysis
    data_flows = graph.query("""
    MATCH (source)-[r]->(target)
    WHERE type(r) STARTS WITH 'DATA_'
    RETURN source.name as source_name,
           type(r) as relationship,
           target.name as target_name,
           source.application as app
    ORDER BY app, source_name
    LIMIT 20
    """)
    
    print('\n🔄 Data Lineage Flows:')
    current_app = None
    for record in data_flows:
        if record['app'] != current_app:
            current_app = record['app']
            print(f'\n   {current_app}:')
        print(f'     {record["source_name"]} --[{record["relationship"]}]--> {record["target_name"]}')
    
    # Data source analysis
    data_sources = graph.query("""
    MATCH (table:Table)-[r]->(query)
    WHERE type(r) STARTS WITH 'DATA_'
    RETURN table.name as table_name,
           type(r) as operation,
           count(*) as usage_count,
           table.application as app
    ORDER BY usage_count DESC, app, table_name
    LIMIT 15
    """)
    
    print('\n🗄️ Database Table Usage:')
    for record in data_sources:
        print(f'   {record["app"]}: {record["table_name"]} ({record["operation"]}: {record["usage_count"]} times)')

# Execute analysis
analyze_enhanced_codebase()

# ========== FINAL SUMMARY ==========

print('\n' + '=' * 80)
print('🎉 ENHANCED JAVA CODE + DATA LINEAGE ANALYSIS v8 COMPLETE!')
print('=' * 80)

print('\n📊 PROCESSING SUMMARY:')
print(f'   📁 Folder/File relationships: {len(folder_records + file_records + app_records)}')
print(f'   🌳 AST relationships extracted: {len(ast_records)}')
print(f'   🤖 LLM relationships extracted: {len(df_llm) if len(df_llm) > 0 else 0}')
print(f'   📊 Final clean dataset: {len(df_final)} relationships')

print('\n🔍 KEY FEATURES:')
print('   ✅ Intelligent variable filtering')
print('   ✅ Transformation pattern detection')
print('   ✅ Multi-application architecture support')
print('   ✅ Inter-application dependency tracking')
print('   ✅ Enhanced AST analysis with context')
print('   ✅ Meaningful relationship extraction')
print('   ✅ Neo4j integration with application context')
print('   ✅ Comprehensive data lineage tracking')
print('   ✅ SQL query pattern detection')
print('   ✅ Data transformation flow analysis')
print('   ✅ API and service data flow mapping')
print('   ✅ Database table usage analytics')
print('   ✅ Production-ready chunked processing')

print('\n🎯 WHAT THIS NOTEBOOK PROVIDES:')
print('   🔍 Complete code lineage analysis')
print('   📊 End-to-end data lineage tracking')
print('   🏗️ Multi-application architecture mapping')
print('   🌐 API endpoint discovery and mapping')
print('   🗄️ Database entity relationship mapping')
print('   🔄 Data transformation pattern detection')
print('   🚀 Scalable processing for large codebases')
print('   📈 Rich Neo4j graph for visualization')

print('\n🚀 READY FOR PRODUCTION USE!')
print('   The enhanced v8 notebook provides comprehensive CODE + DATA lineage')
print('   analysis for multi-application Java codebases with intelligent filtering,')
print('   transformation detection, and end-to-end data flow tracking.')

print('\n🎯 NEXT STEPS:')
print('   1. 🔍 Explore the Neo4j graph at http://localhost:7474')
print('   2. 📊 Run custom Cypher queries for specific analysis')
print('   3. 🔄 Use the data lineage information for impact analysis')
print('   4. 🏗️ Leverage the architecture mapping for system understanding')
print('   5. 📈 Build dashboards and reports from the rich dataset')

print('\n' + '=' * 80)
print('✨ ANALYSIS COMPLETE - ENJOY YOUR COMPREHENSIVE LINEAGE GRAPH! ✨')
print('=' * 80)